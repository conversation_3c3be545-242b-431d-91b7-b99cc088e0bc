// 获取当前域名
const getCurrentDomain = () => {
  const origin = window.location.origin
  // 如果是本地开发环境，使用默认域名
  if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
    return 'https://webapi.catcoinvip.com'
  }
  console.log(origin)
  return origin 
}

export default {
  // 基础URL配置
  baseUrl:'https://webapi.catcoinvip.com',// getCurrentDomain(),
  // API基础URL配置
  apiBaseUrl: 'https://webapi.catcoinvip.com',
  baseRegUrl: 'https://downloade.catcoinvip.com'
  
  // // 基础URL配置
  // baseUrl: 'http://localhost:8094',
  // // API基础URL配置
  // apiBaseUrl: 'http://localhost:8094',
  // baseRegUrl: 'https://reg.huatongyun888.com'
} 